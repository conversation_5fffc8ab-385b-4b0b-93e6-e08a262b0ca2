using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  // 锻造请求
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ForgingReq)]
  [ResponseType(nameof(ForgingResp))]
  public partial class ForgingReq : MaoYouInMessage, ILocationRequest
  {
    public ForgingRecipeIdEnum recipeId; // 锻造配方ID
  }

  // 合成请求
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CraftingReq)]
  [ResponseType(nameof(CraftingResp))]
  public partial class CraftingReq : MaoYouInMessage, ILocationRequest
  {
    public CraftingRecipeIdEnum recipeId; // 合成配方ID
  }
}