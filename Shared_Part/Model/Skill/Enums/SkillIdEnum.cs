namespace MaoYouJi
{
  public enum SkillIdEnum
  {
    None,
    // 天赋被动
    [EnumDescription("精力充沛")]
    Jin<PERSON><PERSON>_<PERSON>ngPei, // "精力充沛", SkillTypeEnum.TALENT_SKILL),
    Sheng<PERSON><PERSON><PERSON><PERSON>_Xue, // "圣龙之血", SkillTypeEnum.TALENT_SKILL), 巴哈姆特被动
    Ji<PERSON>_<PERSON>, // "坚韧", SkillTypeEnum.TALENT_SKILL), 白色宠物蛋被动
    XinYun_<PERSON>engLiang, // "幸运能量", SkillTypeEnum.TALENT_SKILL), 宝石兽蛋被动
    Long_Wei, // "龙威", SkillTypeEnum.TALENT_SKILL), 冰龙蛋被动
    TianShi_ShengGe, // "天使圣歌", SkillTypeEnum.TALENT_SKILL), 炽天使蛋被动
    WuBian_DaDi, // "无边大地", SkillTypeEnum.TALENT_SKILL), 地元素蛋被动
    EMo_PiFu, // "恶魔皮肤", SkillTypeEnum.TALENT_SKILL), 恶魔泡泡被动
    HanBing<PERSON><PERSON>_Kai, // "寒冰之铠", SkillTypeEnum.TALENT_SKILL), 芬利尔蛋被动
    XunJie_ZhiFeng, // "迅捷之风", SkillTypeEnum.TALENT_SKILL), 风元素蛋被动
    Zhen_Shi, // "真视", SkillTypeEnum.TALENT_SKILL), 疯兔蛋被动
    YuanSu_JingZhun, // "元素精准", SkillTypeEnum.TALENT_SKILL), 凤凰蛋被动
    Bo_Tao, // "波涛", SkillTypeEnum.TALENT_SKILL), 海妖蛋被动
    RongYanZhi_Li, // "熔岩之力", SkillTypeEnum.TALENT_SKILL), 火焰魔神蛋被动
    YunShi_BaoFa, // "陨石爆发", SkillTypeEnum.TALENT_SKILL), 火元素蛋被动
    ChengFeng_ErXing, // "乘风而行", SkillTypeEnum.TALENT_SKILL), 迦楼罗被动
    Liu_Huo, // "流火", SkillTypeEnum.TALENT_SKILL), 九尾被动
    KongJuZhi_Ling, // "恐惧之灵", SkillTypeEnum.TALENT_SKILL), 魅影被动
    YanShi_JiaKe, // "岩石甲壳", SkillTypeEnum.TALENT_SKILL), 魔像被动
    ZiRanZhi_Li, // "自然之力", SkillTypeEnum.TALENT_SKILL), 年兽被动
    HuoYan_JieJie, // "火焰结界", SkillTypeEnum.TALENT_SKILL), 七彩圣蛋被动
    XiangRuiZhi_Li, // "祥瑞之力", SkillTypeEnum.TALENT_SKILL), 麒麟被动
    MeiHuo_DiYu, // "魅惑低语", SkillTypeEnum.TALENT_SKILL), 塞壬被动
    BingShuangZhi_Zhu, // "冰霜之助", SkillTypeEnum.TALENT_SKILL), 圣诞泡泡被动
    Ling_Qiao, // "灵巧", SkillTypeEnum.TALENT_SKILL), 圣地灵猴被动
    HaiYang_ShaShou, // "海洋杀手", SkillTypeEnum.TALENT_SKILL), 水獭宝宝被动
    HaiYangZhi_Sheng, // "海洋之声", SkillTypeEnum.TALENT_SKILL), 水元素蛋被动
    ShenSheng_JieJie, // "神圣结界", SkillTypeEnum.TALENT_SKILL), 天使泡泡被动
    XiongZhi_LiLiang, // "熊之力量", SkillTypeEnum.TALENT_SKILL), 玩具熊被动
    NeiXi_GuangHuan, // "内息光环", SkillTypeEnum.TALENT_SKILL), 无尾熊被动
    FengZhi_Wu, // "风之舞", SkillTypeEnum.TALENT_SKILL), 仙人掌被动
    Xun_Jie, // "迅捷", SkillTypeEnum.TALENT_SKILL), 小木猴被动
    YeXing_ChengZhang, // "野性成长", SkillTypeEnum.TALENT_SKILL), 小叶子被动
    HanLeng_ShiYing, // "寒冷适应", SkillTypeEnum.TALENT_SKILL), 鸭嘴兽被动

    // 天赋主动
    ZhiLiao_Shu, // "治疗术", SkillTypeEnum.TALENT_ACTIVE),
    QinXi_Shu, // "清晰术", SkillTypeEnum.TALENT_ACTIVE),
    ShengLong_LieYan, // "圣龙烈焰", SkillTypeEnum.TALENT_ACTIVE), 巴哈姆特主动
    YuDunZhi_Qiang, // "御盾之墙", SkillTypeEnum.TALENT_ACTIVE), 白色宠物蛋主动
    HongShiZhi_Guang, // "红石之光", SkillTypeEnum.TALENT_ACTIVE), // 宝石兽蛋主动
    Jie_Jing, // "结晶", SkillTypeEnum.TALENT_ACTIVE), // 冰龙蛋主动
    TianShiZhi_Nu, // "天使之怒", SkillTypeEnum.TALENT_ACTIVE), // 炽天使蛋主动
    Liu_Sha, // "流沙", SkillTypeEnum.TALENT_ACTIVE), // 地元素蛋主动
    EMoZhi_Song, // "恶魔之颂", SkillTypeEnum.TALENT_ACTIVE), // 恶魔泡泡主动
    JuLangZhi_Shi, // "巨狼之噬", SkillTypeEnum.TALENT_ACTIVE), // 芬利尔蛋主动
    Talent_LongJuanFeng, // "龙卷风", SkillTypeEnum.TALENT_ACTIVE), // 风元素蛋主动
    FengKuang_TuJin, // "疯狂突进", SkillTypeEnum.TALENT_ACTIVE), // 疯兔蛋主动
    RanShaoZhi_Xin, // "燃烧之心", SkillTypeEnum.TALENT_ACTIVE), // 凤凰蛋主动
    Ai_Ge, // "哀歌", SkillTypeEnum.TALENT_ACTIVE), // 海妖蛋主动
    RongYan_ChongJi, // "熔岩冲击", SkillTypeEnum.TALENT_ACTIVE), // 火焰魔神蛋主动
    HuoYanDe_FengNu, // "火焰的愤怒", SkillTypeEnum.TALENT_ACTIVE), // 火元素蛋主动
    DaPeng_ZhanChi, // "大鹏展翅", SkillTypeEnum.TALENT_ACTIVE), // 迦楼罗主动
    Re_Lang, // "热浪", SkillTypeEnum.TALENT_ACTIVE), // 九尾主动
    HuanYingZhi_Ji, // "幻影之击", SkillTypeEnum.TALENT_ACTIVE), // 魅影主动
    YanShiZhi_Quan, // "岩石之拳", SkillTypeEnum.TALENT_ACTIVE), // 魔像主动
    XinLing_JianXiao, // "心灵尖啸", SkillTypeEnum.TALENT_ACTIVE), // 年兽主动
    ZhiYanZhi_Xi, // "炙炎之息", SkillTypeEnum.TALENT_ACTIVE), // 七彩圣蛋主动
    BenTeng_JianTa, // "奔腾践踏", SkillTypeEnum.TALENT_ACTIVE), // 麒麟主动
    HaiYao_ZhiGe, // "海妖之歌", SkillTypeEnum.TALENT_ACTIVE), // 塞壬主动
    BingShuangZhi_Kai, // "冰霜之铠", SkillTypeEnum.TALENT_ACTIVE), // 圣诞泡泡主动
    HeiAnZhi_Zhou, // "黑暗之咒", SkillTypeEnum.TALENT_ACTIVE), // 圣地灵猴主动
    Xue_Xing, // "血腥", SkillTypeEnum.TALENT_ACTIVE), // 水獭宝宝主动
    Shui_Lao, // "水牢", SkillTypeEnum.TALENT_ACTIVE), // 水元素蛋主动
    FangHu_JieJie, // "防护结界", SkillTypeEnum.TALENT_ACTIVE), // 天使泡泡主动
    Bao_Nu, // "暴怒", SkillTypeEnum.TALENT_ACTIVE), // 玩具熊主动
    LanSe_FengBao, // "蓝色风暴", SkillTypeEnum.TALENT_ACTIVE), // 无尾熊主动
    Gang_Ti, // "钢体", SkillTypeEnum.TALENT_ACTIVE), // 仙人掌主动
    HouYing_FenShen, // "猴影分身", SkillTypeEnum.TALENT_ACTIVE), // 小木猴主动
    Yu_He, // "愈合", SkillTypeEnum.TALENT_ACTIVE), // 小叶子主动
    BingDong_GongJi, // "冰冻攻击", SkillTypeEnum.TALENT_ACTIVE), // 鸭嘴兽主动

    // 生活技能
    HunPo_CaiJiShu, // "魂魄采集术", SkillTypeEnum.LIFE_SKILL),
    Da_Gong, // "打工", SkillTypeEnum.LIFE_SKILL),
    Cai_Kuang, // "采矿", SkillTypeEnum.LIFE_SKILL),
    Chan, // "禅", SkillTypeEnum.LIFE_SKILL),
    Dao, // "道", SkillTypeEnum.LIFE_SKILL),
    [EnumDescription("锻造术")]
    Forging_Skill,
    [EnumDescription("合成术")]
    Crafting_Skill,

    // 大逃杀技能
    DaTaoSha_QiangLi, // "强力打击", SkillTypeEnum.BASE_SKILL),
    DaTaoSha_HuoQiu, // "火球术", SkillTypeEnum.BASE_SKILL),
    DaTaoSha_ErGuang, // "耳光", SkillTypeEnum.BASE_SKILL),
    DaTaoSha_MoFaDun, // "魔法盾", SkillTypeEnum.BASE_SKILL),
    DaTaoSha_ZhaoHuanDaXiong, // "召唤大熊", SkillTypeEnum.BASE_SKILL),
    DaTaoSha_YeManYiZhi, // "野蛮意志", SkillTypeEnum.BASE_SKILL),

    // 1.1 战士通用
    QiangLi_DaJi, // "强力打击", SkillTypeEnum.BASE_SKILL),
    ZhiMin_YiJi, // "致命一击", SkillTypeEnum.BASE_SKILL),
    Men_Chou, // "猛抽", SkillTypeEnum.BASE_SKILL),
    Xue_KuangBao, // "血狂暴", SkillTypeEnum.BASE_SKILL),

    // 1.2 暗影刺客
    LongKui_BiRen, // "龙葵匕刃", SkillTypeEnum.JOB_Base_SKILL),
    Jiao_Ti, // "脚踢", SkillTypeEnum.JOB_SKILL),
    Fu_Ji, // "伏击", SkillTypeEnum.JOB_SKILL),
    Zao_Ji, // "凿击", SkillTypeEnum.JOB_SKILL),
    LongKui_LianCi, // "龙葵连刺", SkillTypeEnum.JOB_SKILL),

    // 1.3 乾坤枪手
    BaWan_Qiang, // "霸王枪", SkillTypeEnum.JOB_Base_SKILL),
    PoLu_Qiang, // "破虏枪", SkillTypeEnum.JOB_SKILL),
    PoChu_HuJia, // "破除护甲", SkillTypeEnum.JOB_SKILL),
    SheMin_ChuanCi, // "舍命刺穿", SkillTypeEnum.JOB_SKILL),
    Heng_Sao, // "横扫", SkillTypeEnum.JOB_SKILL),

    // 1.4 皇家卫士
    ShiZi_Jian, // "十字剑", SkillTypeEnum.JOB_Base_SKILL),
    Si_Lie, // "撕裂", SkillTypeEnum.JOB_SKILL),
    Ya_Zhi, // "压制", SkillTypeEnum.JOB_SKILL),
    Shen_Dun, // "圣盾", SkillTypeEnum.JOB_SKILL),
    ShiZi_Zhan, // "十字斩", SkillTypeEnum.JOB_SKILL),
    DunPai_MengJi, // "盾牌猛击", SkillTypeEnum.JOB_SKILL),

    // 1.5 疾风浪客
    JuFeng_DaoFa, // "飓风刀法", SkillTypeEnum.JOB_Base_SKILL),
    CanYin_Dao, // "残影刀", SkillTypeEnum.JOB_SKILL),
    NuFeng_ZhiJi, // "怒风之击", SkillTypeEnum.JOB_SKILL),
    NuFeng_ZhanSha, // "怒风斩杀", SkillTypeEnum.JOB_SKILL),
    MengQin_YiJi, // "猛禽一击", SkillTypeEnum.JOB_SKILL),

    // 1.6 怒灵武者
    YouMin_Zhua, // "幽冥爪", SkillTypeEnum.JOB_Base_SKILL),
    Chu_Xue, // "出血", SkillTypeEnum.JOB_SKILL),
    MinHuo_Zhua, // "冥火爪", SkillTypeEnum.JOB_SKILL),
    KongShouRu_BaiRen, // "空手入白刃(武)", SkillTypeEnum.JOB_SKILL),
    KongShouRu_BaiRen2, // "空手入白刃(技)", SkillTypeEnum.JOB_SKILL),

    // 2.1 法师通用
    HuoQiu_Shu, // "火球术", SkillTypeEnum.BASE_SKILL),
    MoFa_AoYi, // "魔法奥义", SkillTypeEnum.BASE_SKILL),
    Chen_Mo, // "沉默", SkillTypeEnum.BASE_SKILL),
    MoFa_Dun, // "魔法盾", SkillTypeEnum.BASE_SKILL),

    // 2.2 炎术士
    HuoXi_FaShu, // "火系法术", SkillTypeEnum.JOB_Base_SKILL),
    YanBao_Shu, // "炎爆术", SkillTypeEnum.JOB_SKILL),
    HuoYan_PinZhang, // "火焰屏障", SkillTypeEnum.JOB_SKILL),
    LieYan_ZhuoShao, // "烈焰灼烧", SkillTypeEnum.JOB_SKILL),

    // 2.3 冰术士
    BinXi_FaShu, // "冰系法术", SkillTypeEnum.JOB_Base_SKILL),
    HanBin_Jian, // "寒冰箭", SkillTypeEnum.JOB_SKILL),
    ShenDong_ZhiHan, // "深冬之寒", SkillTypeEnum.JOB_SKILL),
    BinShuang_XinXing, // "冰霜新星", SkillTypeEnum.JOB_SKILL),
    HanBin_HuTi, // "寒冰护体", SkillTypeEnum.JOB_SKILL),

    // 2.4 风术士
    FengXi_FaShu, // "风系法术", SkillTypeEnum.JOB_Base_SKILL),
    Feng_ZhiLi, // "风之力", SkillTypeEnum.JOB_SKILL),
    Long_JuanFeng, // "龙卷风", SkillTypeEnum.JOB_SKILL),
    FengJuan_CanYun, // "风卷残云", SkillTypeEnum.JOB_SKILL),
    KuangFeng_LongZhao, // "狂风笼罩", SkillTypeEnum.JOB_SKILL),
    TianFa_ZhiLei, // "天罚之雷", SkillTypeEnum.JOB_SKILL),

    // 2.5 天道士
    Sheng_MoFa, // "圣魔法", SkillTypeEnum.JOB_Base_SKILL),
    ShenSheng_GuangBo, // "神圣光波", SkillTypeEnum.JOB_SKILL),
    ZhengYi_ShenPan, // "正义审判", SkillTypeEnum.JOB_SKILL),
    GuangMing_ShenPan, // "光明审判", SkillTypeEnum.JOB_SKILL),
    CaiJue_ZhiJi, // "裁决之击", SkillTypeEnum.JOB_SKILL),

    // 2.6 天冥士
    An_MoFa, // "暗魔法", SkillTypeEnum.JOB_Base_SKILL),
    AnYin_Jian, // "暗影箭", SkillTypeEnum.JOB_SKILL),
    AnYin_BaoZha, // "暗影爆炸", SkillTypeEnum.JOB_SKILL),
    AnYin_JiaoSha, // "暗影绞杀", SkillTypeEnum.JOB_SKILL),
    SiWang_ChanRao, // "死亡缠绕", SkillTypeEnum.JOB_SKILL),

    // BOSS技能
    LingHun_ZhengJi, // "灵魂震击", SkillTypeEnum.MON_SKILL),
    SiWang_ZuZhou, // "死亡诅咒", SkillTypeEnum.MON_SKILL),
    Mi_Huan, // "迷幻", SkillTypeEnum.MON_SKILL),
    Tu_Beng, // "土崩", SkillTypeEnum.MON_SKILL),
    WuYing_Jiao, // "无影脚", SkillTypeEnum.MON_SKILL),
    QunTi_NianYa, // "群体碾压", SkillTypeEnum.MON_SKILL),
    QiangLi_ShuiZhu, // "强力水柱", SkillTypeEnum.MON_SKILL),
    YuYue_MengJi, // "鱼跃猛击", SkillTypeEnum.MON_SKILL),
    MengZhuo, // "猛啄", SkillTypeEnum.MON_SKILL),
    JianRui_DaMing, // "尖锐打鸣", SkillTypeEnum.MON_SKILL),
    QiangHua_WaiKe, // "强化外壳", SkillTypeEnum.MON_SKILL),
    SiWang_DiaoLing, // "死亡凋零", SkillTypeEnum.MON_SKILL),
    CiEr_JianXiao, // "刺耳尖啸", SkillTypeEnum.MON_SKILL),
    Boss_SiWang_ChanRao, // "死亡缠绕", SkillTypeEnum.MON_SKILL),
    Boss_AnYin_BaoZha, // "暗影爆炸", SkillTypeEnum.MON_SKILL),
    QunTi_AnYin_Jian, // "群体暗影箭", SkillTypeEnum.MON_SKILL),
    MoXiang_SiYi, // "墨香四溢", SkillTypeEnum.MON_SKILL),
    HeiAn_ShenPan, // "黑暗审判", SkillTypeEnum.MON_SKILL),
    ShuiJin_ZhangBi, // "水晶障壁", SkillTypeEnum.MON_SKILL),
    ZhaoHuan_BanFu, // "召唤板斧", SkillTypeEnum.MON_SKILL),
    AnYing_TuXi, // "暗影吐息", SkillTypeEnum.MON_SKILL),
    RongYan, // "熔岩", SkillTypeEnum.MON_SKILL),
    DiZheng, // "地震", SkillTypeEnum.MON_SKILL),
    HuoShan_BaoFa, // "火山爆发", SkillTypeEnum.MON_SKILL),
  }

}