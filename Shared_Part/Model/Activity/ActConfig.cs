using System;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class BaseActConf : ICloneable
  {
    public ActNameEnum name; // 活动名称
    public ActExistType existType; // 活动存在类型
    public ActStateEnum actState; // 活动状态
    public List<string> startTimes; // 活动开始时间，用于日常活动
    public int minLevel; // 最小等级
    public int maxLevel; // 最大等级
    public List<ThingGiveInfo> thingGiveInfos; // 活动奖励，可以为空
    public long startTime; // 活动开始时间，用于一次性活动
    public long endTime; // 活动结束时间，用于一次性活动
    public bool canJustGet; // 是否可以在活动页面领取奖励，0 不可以，1 可以
    public bool needPreCond; // 是否需要前置条件
    public ActGetType getState = ActGetType.Not_Get; // 奖励获取状态

    public object Clone()
    {
      return MemberwiseClone();
    }
  }

  [EnableClass]
  public class ActConfig
  {
    [BsonId]
    public long Id = 8888;
    public DaTaoShaActConf daTaoShaAct;
    public List<BaseActConf> baseActConfs; // 活动配置
  }
}