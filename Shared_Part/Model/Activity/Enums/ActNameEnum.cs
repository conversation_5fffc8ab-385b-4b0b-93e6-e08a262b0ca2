namespace MaoYouJi
{
  public enum ActNameEnum
  {
    None,
    <PERSON>_<PERSON><PERSON>ha, // ("大逃杀"),
    First_Test_Gift, // ("首次测试礼包"),
  }

  public enum ActExistType
  {
    Daily, // ("日常活动"),
    Special, // ("特殊活动"),
    Festival, // ("节日活动"),
    Forever, // ("永久活动"),
  }

  public enum ActStateEnum
  {
    Prepare, // ("预热中"),
    InProgress, // ("进行中"),
    Finished, // ("已完成"),
    Ended, // ("已结束"),
  }

  public enum ActGetType
  {
    Dont_Show, // 不显示
    Not_Get, // 未领取
    Can_Get, // 可领取
    Already_Get, // 已领取
  }
}