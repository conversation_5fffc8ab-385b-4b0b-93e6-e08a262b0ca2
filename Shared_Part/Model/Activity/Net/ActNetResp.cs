using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetActivityListResp)]
  public partial class GetActivityListResp : MaoYouOutMessage, IGlobalResponse
  {
    public List<ActDaoInfo> actList;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.DaTaoShaLearnSkillResp)]
  public partial class DaTaoShaLearnSkillResp : MaoYouOutMessage, ILocationResponse
  {
  }
}