using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetActivityListReq)]
  [ResponseType(nameof(GetActivityListResp))]
  public partial class GetActivityListReq : MaoYouInMessage, IGlobalRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.DaTaoShaLearnSkillReq)]
  [ResponseType(nameof(DaTaoShaLearnSkillResp))]
  public partial class DaTaoShaLearnSkillReq : MaoYouInMessage, ILocationRequest
  {
    public SkillIdEnum skillId;
    public int level;
  }
}