using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(ComActComp))]
  [FriendOf(typeof(ComActComp))]
  public static partial class ComActCompSys
  {
    [EntitySystem]
    private static void Awake(this ComActComp self, List<BaseActConf> baseActConfs)
    {
      baseActConfs.ForEach(conf =>
      {
        self.ComActConfs.AddOrUpdate(conf.name, conf, (key, value) => conf);
      });
    }

    public static ActDaoInfo GetActDaoInfo(this ComActComp self, User user, ActNameEnum actName)
    {
      ActDaoInfo actInfo = new ActDaoInfo();
      actInfo.actName = actName;
      if (actName == ActNameEnum.Da_TaoSha)
      {
        actInfo.existType = ActExistType.Daily;
        DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
        actInfo.actState = daTaoShaActComp.State == 0 ? ActStateEnum.Ended : ActStateEnum.InProgress;
        actInfo.getState = ActGetType.Dont_Show;
        // actInfo.thingGiveInfos = daTaoShaActConf.thingGiveInfos;
        return actInfo;
      }
      else
      {
        if (self.ComActConfs.TryGetValue(actName, out BaseActConf baseActConf))
        {
          actInfo.existType = baseActConf.existType;
          actInfo.startTime = baseActConf.startTime;
          actInfo.endTime = baseActConf.endTime;
          actInfo.thingGiveInfos = baseActConf.thingGiveInfos;
          actInfo.getState = baseActConf.NowGetState(user);
          actInfo.actState = baseActConf.GetActState();
          return actInfo;
        }
        return null;
      }
    }

    public static ActStateEnum GetActState(this BaseActConf self)
    {
      long now = TimeInfo.Instance.ServerNow();
      if (self.startTime != 0 && now < self.startTime)
      {
        return ActStateEnum.Prepare;
      }
      if (self.endTime != 0 && now > self.endTime)
      {
        return ActStateEnum.Ended;
      }
      if (self.startTime != 0 && self.endTime != 0 && now > self.startTime
          && now < self.endTime)
      {
        return ActStateEnum.InProgress;
      }
      return ActStateEnum.Ended;
    }

    // -1不能领取，0满足条件，1可以领取，2已领取
    public static ActGetType NowGetState(this BaseActConf self, User user)
    {
      if (!self.canJustGet || self.GetActState() != ActStateEnum.InProgress)
      {
        return ActGetType.Dont_Show;
      }
      UserGetInfoCnt userGetInfoCnt = user.GetComponent<UserGetInfoCnt>();
      userGetInfoCnt.actGetCnt.TryGetValue(self.name, out int nowGetState);
      if (!self.needPreCond)
      {
        if (nowGetState == 0)
        {
          return ActGetType.Can_Get;
        }
        return ActGetType.Already_Get;
      }
      return ActGetType.Not_Get;
    }
  }
}