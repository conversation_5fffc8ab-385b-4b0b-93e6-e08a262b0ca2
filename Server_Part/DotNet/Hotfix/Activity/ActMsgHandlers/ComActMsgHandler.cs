using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageHand<PERSON>(SceneType.Global)]
  public class GetActListHandler : MessageHandler<Scene, GetActivityListReq, GetActivityListResp>
  {
    protected override async ETTask Run(Scene scene, GetActivityListReq msg, GetActivityListResp resp)
    {
      LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        resp.SetError(logicRet.Message);
        return;
      }
      ComActComp comActComp = scene.GetComponent<ComActComp>();
      List<ActDaoInfo> actList = [comActComp.GetActDaoInfo(user, ActNameEnum.Da_TaoSha)];
      foreach (BaseActConf baseActConf in comActComp.ComActConfs.Values)
      {
        ActDaoInfo actInfo = comActComp.GetActDaoInfo(user, baseActConf.name);
        if (actInfo != null)
        {
          actList.Add(actInfo);
        }
      }
      resp.actList = actList;
      await ETTask.CompletedTask;
    }
  }
}