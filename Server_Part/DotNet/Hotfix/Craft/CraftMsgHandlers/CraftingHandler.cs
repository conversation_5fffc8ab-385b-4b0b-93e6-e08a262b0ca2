namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class CraftingHandler : MessageLocationHandler<MapNode, CraftingReq, CraftingResp>
  {
    protected override async ETTask Run(MapNode nowMap, CraftingReq request, CraftingResp response)
    {
      // 获取用户并验证
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }
      // 获取合成配方
      if (!GlobalInfoCache.Instance.allCraftingRecipeCache.TryGetValue(request.recipeId, out CraftingRecipe recipe))
      {
        response.SetError("合成配方不存在");
        return;
      }

      // 获取用户背包
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      // 检查材料是否足够
      LogicRet materialCheckRet = bagComponent.CheckMaterials(recipe.needThings);
      if (!materialCheckRet.IsSuccess)
      {
        response.SetError(materialCheckRet.Message);
        return;
      }
      // 扣除材料
      LogicRet consumeRet = bagComponent.ConsumeMaterials(recipe.needThings);
      if (!consumeRet.IsSuccess)
      {
        response.SetError(consumeRet.Message);
        return;
      }
      // 给予产出物品
      if (recipe.giveThings != null && recipe.giveThings.Count > 0)
      {
        bagComponent.GiveThing(recipe.giveThings, ThingFromType.Crafting);
      }
      await ETTask.CompletedTask;
    }
  }
}
