2025-05-30 23:42:36.1846 [f-0] StartAsync
2025-05-30 23:42:36.3457 [f-0] CreateCode: MaoYouJi.EntitySystemSingleton
2025-05-30 23:42:36.3457 [f-0] EntitySystemSingleton Awake: MaoYouJi.MailBoxComponentSystem+MaoYouJi_MailBoxComponent_MaoYouJi_MailBoxType_AwakeSystem
2025-05-30 23:42:36.3457 [f-0] EntitySystemSingleton Awake: MaoYouJi.MailBoxComponentSystem+MaoYouJi_MailBoxComponent_DestroySystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessInnerSenderSystem+MaoYouJi_ProcessInnerSender_DestroySystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessInnerSenderSystem+MaoYou<PERSON><PERSON>_ProcessInnerSender_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessInnerSenderSystem+MaoYouJi_ProcessInnerSender_UpdateSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockSystem+MaoYouJi_CoroutineLock_int_long_int_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockSystem+MaoYouJi_CoroutineLock_DestroySystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockComponentSystem+MaoYouJi_CoroutineLockComponent_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockComponentSystem+MaoYouJi_CoroutineLockComponent_UpdateSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockQueueSystem+MaoYouJi_CoroutineLockQueue_int_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockQueueSystem+MaoYouJi_CoroutineLockQueue_DestroySystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockQueueTypeSystem+MaoYouJi_CoroutineLockQueueType_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.TimerComponentSystem+MaoYouJi_TimerComponent_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.TimerComponentSystem+MaoYouJi_TimerComponent_UpdateSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ModeContexSystem+MaoYouJi_ModeContex_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ModeContexSystem+MaoYouJi_ModeContex_DestroySystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionSystem+MaoYouJi_Session_MaoYouJi_AService_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionSystem+MaoYouJi_Session_DestroySystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ObjectWaitSystem+MaoYouJi_ObjectWait_AwakeSystem
2025-05-30 23:42:36.3464 [f-0] EntitySystemSingleton Awake: MaoYouJi.ObjectWaitSystem+MaoYouJi_ObjectWait_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AccountSystem+MaoYouJi_Account_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.GateAccountsComponentSystem+MaoYouJi_GateAccountsComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.ComActCompSys+MaoYouJi_ComActComp_System_Collections_Generic_List_MaoYouJi_BaseActConf__AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.DaTaoShaActCompSys+MaoYouJi_DaTaoShaActComp_MaoYouJi_DaTaoShaActConf_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserDaTaoShaInfoCompSystem+MaoYouJi_UserDaTaoShaInfoComp_MaoYouJi_DaTaoShaActComp_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserDaTaoShaInfoCompSystem+MaoYouJi_UserDaTaoShaInfoComp_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackComponentSystem+MaoYouJi_AttackComponent_LoadSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackComponentSystem+MaoYouJi_AttackComponent_MaoYouJi_FightInfo_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackCtxCompSys+MaoYouJi_AttackCtxComp_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackCtxCompSys+MaoYouJi_AttackCtxComp_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackInCacheSys+MaoYouJi_AttackInCache_MaoYouJi_AttackComponent_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackInCacheSys+MaoYouJi_AttackInCache_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.ActiveJobInfoSystem+MaoYouJi_ActiveJobInfo_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.ActiveJobInfoSystem+MaoYouJi_ActiveJobInfo_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackSongSkillCompSys+MaoYouJi_AttackSongSkillComp_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackSongSkillCompSys+MaoYouJi_AttackSongSkillComp_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackStatusComponentSystem+MaoYouJi_AttackStatusComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackStatusComponentSystem+MaoYouJi_AttackStatusComponent_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneAttackStatusSystem+MaoYouJi_OneAttackStatus_MaoYouJi_AttachStatus_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneAttackStatusSystem+MaoYouJi_OneAttackStatus_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.InFightComponentSystem+MaoYouJi_InFightComponent_MaoYouJi_AttackComponent_MaoYouJi_AttackInCache_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.InFightComponentSystem+MaoYouJi_InFightComponent_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.InFightComponentSystem+MaoYouJi_InFightComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.KilledComponentSystem+MaoYouJi_KilledComponent_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.KilledComponentSystem+MaoYouJi_KilledComponent_AwakeSystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.KilledComponentSystem+MaoYouJi_KilledComponent_DestroySystem
2025-05-30 23:42:36.3473 [f-0] EntitySystemSingleton Awake: MaoYouJi.NormalAttackCompSys+MaoYouJi_NormalAttackComp_long_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.NormalAttackCompSys+MaoYouJi_NormalAttackComp_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.ChatManageCompSys+MaoYouJi_ChatManageComp_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.EquipComponentSystem+MaoYouJi_EquipComponent_LoadSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.EquipComponentSystem+MaoYouJi_EquipComponent_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.GlobalManageCompSys+MaoYouJi_GlobalManageComp_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.TiaoSaoManageCompSys+MaoYouJi_TiaoSaoManageComp_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.FiberMapManageSystem+MaoYouJi_FiberMapManage_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.AutoMoveComponentSystem+MaoYouJi_AutoMoveComponent_long_string_string_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.AutoMoveComponentSystem+MaoYouJi_AutoMoveComponent_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.GenMonComponentSystem+MaoYouJi_GenMonComponent_ConcurrentCollections_ConcurrentHashSet_MaoYouJi_MonsterGen__System_Collections_Concurrent_ConcurrentDictionary_MaoYouJi_MonBaseType_int__AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MapAttackManageSys+MaoYouJi_MapAttackManage_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MapNodeSystem+MaoYouJi_MapNode_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MoveComponentSystem+MaoYouJi_MoveComponent_string_string_int_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.LockInfoSystem+MaoYouJi_LockInfo_MaoYouJi_ActorId_MaoYouJi_CoroutineLock_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.LockInfoSystem+MaoYouJi_LockInfo_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.LocationOneTypeSystem+MaoYouJi_LocationOneType_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.LocationComoponentSystem+MaoYouJi_LocationManagerComoponent_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderComponentSystem+MaoYouJi_MessageLocationSenderOneType_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderComponentSystem+MaoYouJi_MessageLocationSenderOneType_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderManagerComponentSystem+MaoYouJi_MessageLocationSenderComponent_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderSystem+MaoYouJi_MessageLocationSender_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderSystem+MaoYouJi_MessageLocationSender_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageRepeatSenderSystem+MaoYouJi_MessageRepeatSendComponent_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.CellSystem+MaoYouJi_Cell_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.CellSystem+MaoYouJi_Cell_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.DBComponentSystem+MaoYouJi_DBComponent_string_string_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.HttpComponentSystem+MaoYouJi_HttpComponent_string_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.HttpComponentSystem+MaoYouJi_HttpComponent_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessOuterSenderSystem+MaoYouJi_ProcessOuterSender_System_Net_IPEndPoint_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessOuterSenderSystem+MaoYouJi_ProcessOuterSender_UpdateSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessOuterSenderSystem+MaoYouJi_ProcessOuterSender_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterComponentSystem+MaoYouJi_RouterComponent_System_Net_IPEndPoint_string_AwakeSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterComponentSystem+MaoYouJi_RouterComponent_DestroySystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterComponentSystem+MaoYouJi_RouterComponent_UpdateSystem
2025-05-30 23:42:36.3484 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterNodeSystem+MaoYouJi_RouterNode_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterNodeSystem+MaoYouJi_RouterNode_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_MaoYouJi_MonBaseType_MaoYouJi_User_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_MaoYouJi_BaseMonster_MaoYouJi_User_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.NpcInfoSystem+MaoYouJi_NpcInfo_MaoYouJi_BaseNpc_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.NpcInfoSystem+MaoYouJi_NpcInfo_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.NpcManageComponentSystem+MaoYouJi_NpcManageComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RelationComponentSystem+MaoYouJi_RelationComponent_LoadSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RelationComponentSystem+MaoYouJi_RelationComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserFriendInfoSystem+MaoYouJi_UserFriendInfo_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserFriendInfoSystem+MaoYouJi_UserFriendInfo_MaoYouJi_AddFriendInfo_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserFriendInfoSystem+MaoYouJi_UserFriendInfo_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RelationManageCompSys+MaoYouJi_RelationManageComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.BenchmarkClientComponentSystem+MaoYouJi_BenchmarkClientComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.BenchmarkServerComponentSystem+MaoYouJi_BenchmarkServerComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.PlayerSystem+MaoYouJi_Player_long_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionPlayerComponentSystem+MaoYouJi_SessionPlayerComponent_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionPlayerComponentSystem+MaoYouJi_SessionPlayerComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RealmPingGateComponetSystem+MaoYouJi_RealmPingGateComponet_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RealmPingGateComponetSystem+MaoYouJi_RealmPingGateComponet_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RobotManagerComponentSystem+MaoYouJi_RobotManagerComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.RobotManagerComponentSystem+MaoYouJi_RobotManagerComponent_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.WatcherComponentSystem+MaoYouJi_WatcherComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.SkillComponentSystem+MaoYouJi_SkillComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.SkillManageComponentSystem+MaoYouJi_SkillManageComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneTaskSystem+MaoYouJi_OneTask_MaoYouJi_BaseTask_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneTaskSystem+MaoYouJi_OneTask_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TaskComponentSystem+MaoYouJi_TaskComponent_LoadSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TaskComponentSystem+MaoYouJi_TaskComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.BiaoCheInfoCompSystem+MaoYouJi_BiaoCheInfoComp_MaoYouJi_MonsterInfo_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.BiaoCheInfoCompSystem+MaoYouJi_BiaoCheInfoComp_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamMemberSystem+MaoYouJi_TeamMember_MaoYouJi_User_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamMemberSystem+MaoYouJi_TeamMember_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamInfoSystem+MaoYouJi_TeamInfo_MaoYouJi_ClientCreateTeamMsg_MaoYouJi_User_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamInfoSystem+MaoYouJi_TeamInfo_DestroySystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamInfoSystem+MaoYouJi_TeamInfo_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamManageCompSystem+MaoYouJi_TeamManageComponent_AwakeSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserSystem+MaoYouJi_User_LoadSystem
2025-05-30 23:42:36.3494 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserSystem+MaoYouJi_User_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserActorComponentSystem+MaoYouJi_UserActorComponent_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.DailyCntInfoSystem+MaoYouJi_UserDailyCntInfo_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserGetInfoCntSystem+MaoYouJi_UserGetInfoCnt_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserStateCompSys+MaoYouJi_UserStateComp_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneUserStateCompSys+MaoYouJi_OneUserStateComp_MaoYouJi_UserStateDetail_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneUserStateCompSys+MaoYouJi_OneUserStateComp_DestroySystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserTimeInfoSystem+MaoYouJi_UserTimeInfo_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserVipFuncInfoSystem+MaoYouJi_UserVipFuncInfo_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.FiberUsersComponentSystem+MaoYouJi_FiberUsersComponent_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.VipAfkCompSys+MaoYouJi_VipAfkComp_MaoYouJi_AfkSystemType_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.ConsoleComponentSystem+MaoYouJi_ConsoleComponent_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_System_Net_IPEndPoint_MaoYouJi_NetworkProtocol_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_System_Net_Sockets_AddressFamily_MaoYouJi_NetworkProtocol_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_UpdateSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_DestroySystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionAcceptTimeoutComponentHelper+MaoYouJi_SessionAcceptTimeoutComponent_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionAcceptTimeoutComponentHelper+MaoYouJi_SessionAcceptTimeoutComponent_DestroySystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionIdleCheckerComponentSystem+MaoYouJi_SessionIdleCheckerComponent_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionIdleCheckerComponentSystem+MaoYouJi_SessionIdleCheckerComponent_DestroySystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.PathfindingComponentSystem+MaoYouJi_PathfindingComponent_string_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.PathfindingComponentSystem+MaoYouJi_PathfindingComponent_DestroySystem
2025-05-30 23:42:36.3503 [f-0] EntitySystemSingleton Awake: MaoYouJi.BagGetSystem+MaoYouJi_BagComponent_MaoYouJi_BagType_int_AwakeSystem
2025-05-30 23:42:36.3503 [f-0] CreateCode: MaoYouJi.MessageDispatcher
2025-05-30 23:42:36.3544 [f-0] CreateCode: MaoYouJi.EventSystem
2025-05-30 23:42:36.3586 [f-0] CreateCode: MaoYouJi.HttpDispatcher
2025-05-30 23:42:36.3586 [f-0] CreateCode: MaoYouJi.ConsoleDispatcher
2025-05-30 23:42:36.3586 [f-0] CreateCode: MaoYouJi.MessageSessionDispatcher
2025-05-30 23:42:36.3603 [f-0] CreateCode: MaoYouJi.NumericWatcherComponent
2025-05-30 23:42:36.3638 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartMachineConfigCategory.txt
2025-05-30 23:42:36.3638 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartMachineConfig","_id":1,"InnerIP":"127.0.0.1","OuterIP":"127.0.0.1","WatcherPort":"10000"}],
]}

2025-05-30 23:42:36.3638 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartProcessConfigCategory.txt
2025-05-30 23:42:36.3638 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartProcessConfig","_id":1,"MachineId":1,"Port":20001}],
]}

2025-05-30 23:42:36.3644 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartSceneConfigCategory.txt
2025-05-30 23:42:36.3644 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartSceneConfig","_id":1,"Process":1,"Zone":1,"SceneType":"Realm","Name":"Realm","Port":30002}],
[2, {"_t":"StartSceneConfig","_id":2,"Process":1,"Zone":2,"SceneType":"Gate","Name":"Gate1","Port":30003}],
[3, {"_t":"StartSceneConfig","_id":3,"Process":1,"Zone":2,"SceneType":"Gate","Name":"Gate2","Port":30004}],
[4, {"_t":"StartSceneConfig","_id":4,"Process":1,"Zone":2,"SceneType":"Location","Name":"Location","Port":0}],
[5, {"_t":"StartSceneConfig","_id":5,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map1","Port":30005}],
[6, {"_t":"StartSceneConfig","_id":6,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map2","Port":30006}],
[7, {"_t":"StartSceneConfig","_id":7,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map3","Port":30007}],
[8, {"_t":"StartSceneConfig","_id":8,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map4","Port":30008}],
[9, {"_t":"StartSceneConfig","_id":9,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map5","Port":30009}],
[10, {"_t":"StartSceneConfig","_id":10,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack1","Port":30010}],
[11, {"_t":"StartSceneConfig","_id":11,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack2","Port":30011}],
[12, {"_t":"StartSceneConfig","_id":12,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack3","Port":30012}],
[13, {"_t":"StartSceneConfig","_id":13,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack4","Port":30013}],
[14, {"_t":"StartSceneConfig","_id":14,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack5","Port":30014}],
[15, {"_t":"StartSceneConfig","_id":15,"Process":1,"Zone":2,"SceneType":"Social","Name":"Social1","Port":30015}],
[300, {"_t":"StartSceneConfig","_id":300,"Process":1,"Zone":1,"SceneType":"RouterManager","Name":"RouterManager","Port":30300}],
[301, {"_t":"StartSceneConfig","_id":301,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router01","Port":30301}],
[302, {"_t":"StartSceneConfig","_id":302,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router02","Port":30302}],
[303, {"_t":"StartSceneConfig","_id":303,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router03","Port":30303}],
[304, {"_t":"StartSceneConfig","_id":304,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router04","Port":30304}],
]}

2025-05-30 23:42:36.3644 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartZoneConfigCategory.txt
2025-05-30 23:42:36.3644 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartZoneConfig","_id":1,"DBConnection":"mongodb://127.0.0.1","DBName":"MaoYouDaLu", "ServerName": "网关服务器"}],
[2, {"_t":"StartZoneConfig","_id":2,"DBConnection":"mongodb://127.0.0.1","DBName":"MaoYouDaLu", "ServerName": "测试服务器", "UserType": "ADMIN", "MaxOnlineNum": 4000}],
]}

2025-05-30 23:42:36.3644 [f-0] configFilePath: ../Config/Json/s/UnitConfigCategory.txt
2025-05-30 23:42:36.3644 [f-0] output[configType]: {
  "dict": []
}
2025-05-30 23:42:36.3848 [f-0] scene create: Main -1 1
2025-05-30 23:42:36.3926 [f--1] AddQueue: -1
2025-05-30 23:42:36.3949 [f--1] scene create: NetInner -2 1
2025-05-30 23:42:36.3990 [f--2] AddQueue: -2
2025-05-30 23:42:36.6275 [f--1] QuartzScheduler start
2025-05-30 23:42:36.6300 [f--1] LocationConfig: Location
2025-05-30 23:42:36.6300 [f--1] scene create: Global -5 1
2025-05-30 23:42:36.6306 [f--5] AddQueue: -5
2025-05-30 23:42:36.6306 [f--5] 初始化全局数据，Zone: 2
2025-05-30 23:42:36.7744 [f--1] mallShopList: 93
2025-05-30 23:42:36.7883 [f--1] comShopList: 277
2025-05-30 23:42:36.7973 [f--1] foodList: 29
2025-05-30 23:42:36.8034 [f--1] taskThingList: 130
2025-05-30 23:42:36.8117 [f--1] treasureList: 139
2025-05-30 23:42:36.8165 [f--1] materialList: 189
2025-05-30 23:42:36.8660 [f--1] equipList: 1558
2025-05-30 23:42:36.8697 [f--1] mineList: 39
2025-05-30 23:42:36.8931 [f--1] monsterList: 237
2025-05-30 23:42:36.9014 [f--1] baseAttackList: 198
2025-05-30 23:42:36.9163 [f--1] baseSkillList: 144
2025-05-30 23:42:36.9572 [f--1] baseTaskList: 518
2025-05-30 23:42:36.9572 [f--1] scene create: Realm 1 1
2025-05-30 23:42:36.9586 [f-1] AddQueue: 1
2025-05-30 23:42:36.9624 [f--1] scene create: Gate 2 1
2025-05-30 23:42:36.9632 [f-2] AddQueue: 2
2025-05-30 23:42:36.9648 [f--1] scene create: Gate 3 1
2025-05-30 23:42:36.9648 [f-3] AddQueue: 3
2025-05-30 23:42:36.9661 [f--1] scene create: Location 4 1
2025-05-30 23:42:36.9665 [f-4] AddQueue: 4
2025-05-30 23:42:36.9665 [f--1] scene create: Map 5 1
2025-05-30 23:42:36.9688 [f-5] AddQueue: 5
2025-05-30 23:42:36.9719 [f--1] scene create: Map 6 1
2025-05-30 23:42:36.9730 [f-6] AddQueue: 6
2025-05-30 23:42:36.9743 [f--1] scene create: Map 7 1
2025-05-30 23:42:36.9752 [f-7] AddQueue: 7
2025-05-30 23:42:36.9767 [f--1] scene create: Map 8 1
2025-05-30 23:42:36.9775 [f-8] AddQueue: 8
2025-05-30 23:42:36.9775 [f--1] scene create: Map 9 1
2025-05-30 23:42:36.9786 [f-9] AddQueue: 9
2025-05-30 23:42:36.9786 [f--1] scene create: Attack 10 1
2025-05-30 23:42:36.9805 [f-10] AddQueue: 10
2025-05-30 23:42:36.9816 [f--1] scene create: Attack 11 1
2025-05-30 23:42:36.9862 [f-11] AddQueue: 11
2025-05-30 23:42:36.9874 [f--1] scene create: Attack 12 1
2025-05-30 23:42:36.9884 [f-12] AddQueue: 12
2025-05-30 23:42:36.9884 [f--1] scene create: Attack 13 1
2025-05-30 23:42:36.9896 [f-13] AddQueue: 13
2025-05-30 23:42:36.9896 [f--1] scene create: Attack 14 1
2025-05-30 23:42:36.9907 [f-14] AddQueue: 14
2025-05-30 23:42:36.9907 [f--1] scene create: Social 15 1
2025-05-30 23:42:36.9926 [f-15] AddQueue: 15
2025-05-30 23:42:36.9935 [f--1] scene create: RouterManager 300 1
2025-05-30 23:42:37.0275 [f--1] scene create: Router 301 1
2025-05-30 23:42:37.0308 [f-301] Router create: 301
2025-05-30 23:42:37.0320 [f--1] scene create: Router 302 1
2025-05-30 23:42:37.0320 [f-302] Router create: 302
2025-05-30 23:42:37.0331 [f--1] scene create: Router 303 1
2025-05-30 23:42:37.0360 [f-303] Router create: 303
2025-05-30 23:42:37.0373 [f--1] scene create: Router 304 1
2025-05-30 23:42:37.0384 [f-304] Router create: 304
2025-05-30 23:42:37.2675 [f-7] 地图节点加载开始，地图节点总数：2040
2025-05-30 23:42:37.2675 [f-8] 地图节点加载开始，地图节点总数：2040
2025-05-30 23:42:37.2836 [f-9] 地图节点加载开始，地图节点总数：2040
2025-05-30 23:42:37.2848 [f-6] 地图节点加载开始，地图节点总数：2040
2025-05-30 23:42:37.2860 [f-5] 地图节点加载开始，地图节点总数：2040
2025-05-30 23:42:37.3113 [f-5] Map1 地图节点加载完成，当前地图节点数量：60
2025-05-30 23:42:37.5252 [f-9] Map5 地图节点加载完成，当前地图节点数量：410
2025-05-30 23:42:37.5352 [f-6] Map2 地图节点加载完成，当前地图节点数量：484
2025-05-30 23:42:37.5468 [f-8] Map4 地图节点加载完成，当前地图节点数量：555
2025-05-30 23:42:37.5594 [f-7] 地图点构建完成：47
2025-05-30 23:42:37.7951 [f-7] 地图距离计算完成：2040
2025-05-30 23:42:37.7980 [f-7] 城市距离计算完成：35
2025-05-30 23:42:37.7980 [f-7] Map3 地图节点加载完成，当前地图节点数量：531
2025-05-30 23:43:01.7005 [f-303] router new: outerConn: 2202675340 innerConn: 0 127.0.0.1:58494
2025-05-30 23:43:01.7005 [f-303] router create: 127.0.0.1:30002 2202675340 0 127.0.0.1:58494
2025-05-30 23:43:01.8382 [f-303] kcp router syn: 2202675340 0 127.0.0.1:30002 127.0.0.1:58494
2025-05-30 23:43:01.8400 [f-1] channel create: 2147483650 2202675340 127.0.0.1:54217 Accept
2025-05-30 23:43:01.8416 [f-1] session create: zone: 1 id: 2147483650 1748619781841 
2025-05-30 23:43:01.8420 [f-1] kservice syn: 2147483650 2202675340 2147483650 127.0.0.1:54217
2025-05-30 23:43:01.8420 [f-303] kcp router ack: 2202675340 2147483650 127.0.0.1:58494
2025-05-30 23:43:02.2290 [f-1][r-771420147] tap tap login result: 0 cQPq2VZTPtrQUKHhblcZeg== Wd8SUnqB8IRT9hmXibjLOQ==
2025-05-30 23:43:02.8713 [f-304] router new: outerConn: 3381774984 innerConn: 0 127.0.0.1:58510
2025-05-30 23:43:02.8713 [f-304] router create: 127.0.0.1:30004 3381774984 0 127.0.0.1:58510
2025-05-30 23:43:02.8978 [f-303] kcp router outer fin: 2202675340 2147483650 127.0.0.1:30002
2025-05-30 23:43:02.8978 [f-303] kcp router outer fin: 2202675340 2147483650 127.0.0.1:30002
2025-05-30 23:43:02.8978 [f-303] kcp router outer fin: 2202675340 2147483650 127.0.0.1:30002
2025-05-30 23:43:02.8978 [f-1] kservice recv fin: 2147483650 2202675340 0
2025-05-30 23:43:02.8978 [f-304] kcp router syn: 3381774984 0 127.0.0.1:30004 127.0.0.1:58510
2025-05-30 23:43:02.8984 [f-3] channel create: 2147483652 3381774984 127.0.0.1:65305 Accept
2025-05-30 23:43:02.8984 [f-3] session create: zone: 2 id: 2147483652 1748619782898 
2025-05-30 23:43:02.8984 [f-3] kservice syn: 2147483652 3381774984 2147483652 127.0.0.1:65305
2025-05-30 23:43:02.8984 [f-1] channel dispose: 2147483650 2202675340 100208
2025-05-30 23:43:02.8984 [f-304] kcp router ack: 3381774984 2147483652 127.0.0.1:58510
2025-05-30 23:43:02.8999 [f-1] session dispose: 127.0.0.1:58494 id: 2147483650 ErrorCode: 100208, please see ErrorCode.cs! 1748619782899
2025-05-30 23:43:04.6865 [f-3][r-771223535] 转移用户: 1238372941060900 462244068525676592 3 9
2025-05-30 23:43:28.8972 [f-303] TChannel OnError: 100208 127.0.0.1:58494
2025-05-30 23:43:28.8976 [f-303] channel dispose: 2147483649 127.0.0.1:58494 0
2025-05-30 23:43:28.9008 [f-304] TChannel OnError: 100208 127.0.0.1:58510
2025-05-30 23:43:28.9008 [f-304] channel dispose: 2147483651 127.0.0.1:58510 0
2025-05-30 23:43:36.7197 [f--5][r-769126365] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:43:43.1268 [f-303] router node remove: 2202675340 2147483650 110401
2025-05-30 23:43:43.1268 [f-303] router remove: 2202675340 outerConn: 2202675340 innerConn: 2147483650
2025-05-30 23:43:58.9038 [f-3] session timeout: 2147483652 1748619838903 1748619807251 1748619807252 31652 31651
2025-05-30 23:43:58.9175 [f-3] channel dispose: 2147483652 3381774984 110311
2025-05-30 23:43:58.9181 [f-3] channel send fin: 2147483652 3381774984 127.0.0.1:65305 110311
2025-05-30 23:43:58.9181 [f-3] session dispose: 127.0.0.1:58510 id: 2147483652 ErrorCode: 110311, please see ErrorCode.cs! 1748619838918
2025-05-30 23:43:58.9189 [f-304] kcp router inner fin: 3381774984 2147483652 127.0.0.1:58510
2025-05-30 23:43:58.9189 [f-304] kcp router inner fin: 3381774984 2147483652 127.0.0.1:58510
2025-05-30 23:43:58.9189 [f-304] kcp router inner fin: 3381774984 2147483652 127.0.0.1:58510
2025-05-30 23:43:58.9189 [f-4] location get key: 1238372941060900 actorId: 0:0:0
2025-05-30 23:44:08.1560 [f-304] router node remove: 3381774984 2147483652 110401
2025-05-30 23:44:08.1560 [f-304] router remove: 3381774984 outerConn: 3381774984 innerConn: 2147483652
2025-05-30 23:44:36.7073 [f--5][r-765194174] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:44:56.2451 [f-301] router new: outerConn: 2787093883 innerConn: 0 127.0.0.1:58777
2025-05-30 23:44:56.2451 [f-301] router create: 127.0.0.1:30002 2787093883 0 127.0.0.1:58777
2025-05-30 23:44:56.3066 [f-301] kcp router syn: 2787093883 0 127.0.0.1:30002 127.0.0.1:58777
2025-05-30 23:44:56.3075 [f-1] channel create: 2147483654 2787093883 127.0.0.1:56961 Accept
2025-05-30 23:44:56.3075 [f-1] session create: zone: 1 id: 2147483654 1748619896307 
2025-05-30 23:44:56.3075 [f-1] kservice syn: 2147483654 2787093883 2147483654 127.0.0.1:56961
2025-05-30 23:44:56.3087 [f-301] kcp router ack: 2787093883 2147483654 127.0.0.1:58777
2025-05-30 23:44:56.7564 [f-1][r-763883443] tap tap login result: 0 cQPq2VZTPtrQUKHhblcZeg== Wd8SUnqB8IRT9hmXibjLOQ==
2025-05-30 23:44:57.3428 [f-303] router new: outerConn: 2238235918 innerConn: 0 127.0.0.1:58779
2025-05-30 23:44:57.3428 [f-303] router create: 127.0.0.1:30004 2238235918 0 127.0.0.1:58779
2025-05-30 23:44:57.3676 [f-301] kcp router outer fin: 2787093883 2147483654 127.0.0.1:30002
2025-05-30 23:44:57.3676 [f-303] kcp router syn: 2238235918 0 127.0.0.1:30004 127.0.0.1:58779
2025-05-30 23:44:57.3676 [f-301] kcp router outer fin: 2787093883 2147483654 127.0.0.1:30002
2025-05-30 23:44:57.3676 [f-301] kcp router outer fin: 2787093883 2147483654 127.0.0.1:30002
2025-05-30 23:44:57.3676 [f-3] channel create: 2147483656 2238235918 127.0.0.1:54217 Accept
2025-05-30 23:44:57.3676 [f-3] session create: zone: 2 id: 2147483656 1748619897367 
2025-05-30 23:44:57.3676 [f-1] kservice recv fin: 2147483654 2787093883 0
2025-05-30 23:44:57.3676 [f-3] kservice syn: 2147483656 2238235918 2147483656 127.0.0.1:54217
2025-05-30 23:44:57.3676 [f-1] channel dispose: 2147483654 2787093883 100208
2025-05-30 23:44:57.3681 [f-1] session dispose: 127.0.0.1:58777 id: 2147483654 ErrorCode: 100208, please see ErrorCode.cs! 1748619897368
2025-05-30 23:44:57.3681 [f-303] kcp router ack: 2238235918 2147483656 127.0.0.1:58779
2025-05-30 23:45:08.1346 [f-303] TChannel OnError: 100208 127.0.0.1:58779
2025-05-30 23:45:08.1346 [f-303] channel dispose: 2147483655 127.0.0.1:58779 0
2025-05-30 23:45:08.1346 [f-301] TChannel OnError: 100208 127.0.0.1:58777
2025-05-30 23:45:08.1346 [f-301] channel dispose: 2147483653 127.0.0.1:58777 0
2025-05-30 23:45:36.7060 [f--5][r-761261978] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:45:38.2421 [f-301] router node remove: 2787093883 2147483654 110401
2025-05-30 23:45:38.2421 [f-301] router remove: 2787093883 outerConn: 2787093883 innerConn: 2147483654
2025-05-30 23:45:39.3760 [f-3] session timeout: 2147483656 1748619939375 1748619907478 1748619907479 31897 31896
2025-05-30 23:45:39.3774 [f-3] channel dispose: 2147483656 2238235918 110311
2025-05-30 23:45:39.3774 [f-3] channel send fin: 2147483656 2238235918 127.0.0.1:54217 110311
2025-05-30 23:45:39.3774 [f-3] session dispose: 127.0.0.1:58779 id: 2147483656 ErrorCode: 110311, please see ErrorCode.cs! 1748619939377
2025-05-30 23:45:39.3774 [f-303] kcp router inner fin: 2238235918 2147483656 127.0.0.1:58779
2025-05-30 23:45:39.3774 [f-303] kcp router inner fin: 2238235918 2147483656 127.0.0.1:58779
2025-05-30 23:45:39.3774 [f-303] kcp router inner fin: 2238235918 2147483656 127.0.0.1:58779
2025-05-30 23:45:39.3774 [f-4] location get key: 1238372941060900 actorId: 0:0:0
2025-05-30 23:45:48.2852 [f-303] router node remove: 2238235918 2147483656 110401
2025-05-30 23:45:48.2852 [f-303] router remove: 2238235918 outerConn: 2238235918 innerConn: 2147483656
2025-05-30 23:45:48.5259 [f-301] router new: outerConn: 3563194783 innerConn: 0 127.0.0.1:58968
2025-05-30 23:45:48.5259 [f-301] router create: 127.0.0.1:30002 3563194783 0 127.0.0.1:58968
2025-05-30 23:45:48.5902 [f-301] kcp router syn: 3563194783 0 127.0.0.1:30002 127.0.0.1:58968
2025-05-30 23:45:48.5910 [f-1] channel create: 2147483658 3563194783 127.0.0.1:56961 Accept
2025-05-30 23:45:48.5910 [f-1] session create: zone: 1 id: 2147483658 1748619948591 
2025-05-30 23:45:48.5910 [f-1] kservice syn: 2147483658 3563194783 2147483658 127.0.0.1:56961
2025-05-30 23:45:48.5920 [f-301] kcp router ack: 3563194783 2147483658 127.0.0.1:58968
2025-05-30 23:45:49.0134 [f-1][r-760475539] tap tap login result: 0 cQPq2VZTPtrQUKHhblcZeg== Wd8SUnqB8IRT9hmXibjLOQ==
2025-05-30 23:45:49.6107 [f-302] router new: outerConn: 4107833843 innerConn: 0 127.0.0.1:58970
2025-05-30 23:45:49.6107 [f-302] router create: 127.0.0.1:30004 4107833843 0 127.0.0.1:58970
2025-05-30 23:45:49.6334 [f-301] kcp router outer fin: 3563194783 2147483658 127.0.0.1:30002
2025-05-30 23:45:49.6334 [f-301] kcp router outer fin: 3563194783 2147483658 127.0.0.1:30002
2025-05-30 23:45:49.6334 [f-301] kcp router outer fin: 3563194783 2147483658 127.0.0.1:30002
2025-05-30 23:45:49.6334 [f-302] kcp router syn: 4107833843 0 127.0.0.1:30004 127.0.0.1:58970
2025-05-30 23:45:49.6334 [f-1] kservice recv fin: 2147483658 3563194783 0
2025-05-30 23:45:49.6334 [f-1] channel dispose: 2147483658 3563194783 100208
2025-05-30 23:45:49.6334 [f-1] session dispose: 127.0.0.1:58968 id: 2147483658 ErrorCode: 100208, please see ErrorCode.cs! 1748619949634
2025-05-30 23:45:49.6345 [f-3] channel create: 2147483660 4107833843 127.0.0.1:59856 Accept
2025-05-30 23:45:49.6345 [f-3] session create: zone: 2 id: 2147483660 1748619949634 
2025-05-30 23:45:49.6345 [f-3] kservice syn: 2147483660 4107833843 2147483660 127.0.0.1:59856
2025-05-30 23:45:49.6345 [f-302] kcp router ack: 4107833843 2147483660 127.0.0.1:58970
2025-05-30 23:46:00.9833 [f-301] TChannel OnError: 100208 127.0.0.1:58968
2025-05-30 23:46:00.9833 [f-301] channel dispose: 2147483657 127.0.0.1:58968 0
2025-05-30 23:46:00.9857 [f-302] TChannel OnError: 100208 127.0.0.1:58970
2025-05-30 23:46:00.9857 [f-302] channel dispose: 2147483659 127.0.0.1:58970 0
2025-05-30 23:46:30.3058 [f-301] router node remove: 3563194783 2147483658 110401
2025-05-30 23:46:30.3058 [f-301] router remove: 3563194783 outerConn: 3563194783 innerConn: 2147483658
2025-05-30 23:46:31.6399 [f-3] session timeout: 2147483660 1748619991639 1748619959781 1748619959781 31858 31858
2025-05-30 23:46:31.6417 [f-3] channel dispose: 2147483660 4107833843 110311
2025-05-30 23:46:31.6417 [f-3] channel send fin: 2147483660 4107833843 127.0.0.1:59856 110311
2025-05-30 23:46:31.6417 [f-3] session dispose: 127.0.0.1:58970 id: 2147483660 ErrorCode: 110311, please see ErrorCode.cs! 1748619991641
2025-05-30 23:46:31.6417 [f-4] location get key: 1238372941060900 actorId: 0:0:0
2025-05-30 23:46:31.6417 [f-302] kcp router inner fin: 4107833843 2147483660 127.0.0.1:58970
2025-05-30 23:46:31.6417 [f-302] kcp router inner fin: 4107833843 2147483660 127.0.0.1:58970
2025-05-30 23:46:31.6417 [f-302] kcp router inner fin: 4107833843 2147483660 127.0.0.1:58970
2025-05-30 23:46:36.7094 [f--5][r-757329782] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:46:40.3461 [f-302] router node remove: 4107833843 2147483660 110401
2025-05-30 23:46:40.3461 [f-302] router remove: 4107833843 outerConn: 4107833843 innerConn: 2147483660
2025-05-30 23:47:36.7051 [f--5][r-753397591] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:47:36.7100 [f--5][r-753397590] InnerGlobalTimerPer_5_MinMsgHandler
2025-05-30 23:48:08.9545 [f-302] router new: outerConn: 3517885775 innerConn: 0 127.0.0.1:59219
2025-05-30 23:48:08.9545 [f-302] router create: 127.0.0.1:30002 3517885775 0 127.0.0.1:59219
2025-05-30 23:48:09.0162 [f-302] kcp router syn: 3517885775 0 127.0.0.1:30002 127.0.0.1:59219
2025-05-30 23:48:09.0165 [f-1] channel create: 2147483662 3517885775 127.0.0.1:59856 Accept
2025-05-30 23:48:09.0165 [f-1] session create: zone: 1 id: 2147483662 1748620089016 
2025-05-30 23:48:09.0165 [f-1] kservice syn: 2147483662 3517885775 2147483662 127.0.0.1:59856
2025-05-30 23:48:09.0173 [f-302] kcp router ack: 3517885775 2147483662 127.0.0.1:59219
2025-05-30 23:48:09.4044 [f-1][r-751234885] tap tap login result: 0 cQPq2VZTPtrQUKHhblcZeg== Wd8SUnqB8IRT9hmXibjLOQ==
2025-05-30 23:48:10.1154 [f-303] router new: outerConn: 2816277023 innerConn: 0 127.0.0.1:59221
2025-05-30 23:48:10.1154 [f-303] router create: 127.0.0.1:30004 2816277023 0 127.0.0.1:59221
2025-05-30 23:48:10.1363 [f-302] kcp router outer fin: 3517885775 2147483662 127.0.0.1:30002
2025-05-30 23:48:10.1363 [f-303] kcp router syn: 2816277023 0 127.0.0.1:30004 127.0.0.1:59221
2025-05-30 23:48:10.1363 [f-302] kcp router outer fin: 3517885775 2147483662 127.0.0.1:30002
2025-05-30 23:48:10.1363 [f-302] kcp router outer fin: 3517885775 2147483662 127.0.0.1:30002
2025-05-30 23:48:10.1363 [f-1] kservice recv fin: 2147483662 3517885775 0
2025-05-30 23:48:10.1363 [f-1] channel dispose: 2147483662 3517885775 100208
2025-05-30 23:48:10.1363 [f-1] session dispose: 127.0.0.1:59219 id: 2147483662 ErrorCode: 100208, please see ErrorCode.cs! 1748620090136
2025-05-30 23:48:10.1374 [f-3] channel create: 2147483664 2816277023 127.0.0.1:54217 Accept
2025-05-30 23:48:10.1374 [f-3] session create: zone: 2 id: 2147483664 1748620090137 
2025-05-30 23:48:10.1374 [f-3] kservice syn: 2147483664 2816277023 2147483664 127.0.0.1:54217
2025-05-30 23:48:10.1385 [f-303] kcp router ack: 2816277023 2147483664 127.0.0.1:59221
2025-05-30 23:48:33.6906 [f-302] TChannel OnError: 100208 127.0.0.1:59219
2025-05-30 23:48:33.6906 [f-302] channel dispose: 2147483661 127.0.0.1:59219 0
2025-05-30 23:48:33.6906 [f-303] TChannel OnError: 100208 127.0.0.1:59221
2025-05-30 23:48:33.6906 [f-303] channel dispose: 2147483663 127.0.0.1:59221 0
2025-05-30 23:48:36.7087 [f--5][r-749465394] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:48:50.5102 [f-302] router node remove: 3517885775 2147483662 110401
2025-05-30 23:48:50.5102 [f-302] router remove: 3517885775 outerConn: 3517885775 innerConn: 2147483662
2025-05-30 23:49:04.1449 [f-3] session timeout: 2147483664 1748620144144 1748620112435 1748620112435 31709 31709
2025-05-30 23:49:04.1477 [f-3] channel dispose: 2147483664 2816277023 110311
2025-05-30 23:49:04.1477 [f-4] location get key: 1238372941060900 actorId: 0:0:0
2025-05-30 23:49:04.1477 [f-3] channel send fin: 2147483664 2816277023 127.0.0.1:54217 110311
2025-05-30 23:49:04.1485 [f-3] session dispose: 127.0.0.1:59221 id: 2147483664 ErrorCode: 110311, please see ErrorCode.cs! 1748620144148
2025-05-30 23:49:04.1485 [f-303] kcp router inner fin: 2816277023 2147483664 127.0.0.1:59221
2025-05-30 23:49:04.1485 [f-303] kcp router inner fin: 2816277023 2147483664 127.0.0.1:59221
2025-05-30 23:49:04.1485 [f-303] kcp router inner fin: 2816277023 2147483664 127.0.0.1:59221
2025-05-30 23:49:12.5392 [f-303] router node remove: 2816277023 2147483664 110401
2025-05-30 23:49:12.5397 [f-303] router remove: 2816277023 outerConn: 2816277023 innerConn: 2147483664
2025-05-30 23:49:19.3421 [f-304] router new: outerConn: 3631344834 innerConn: 0 127.0.0.1:59418
2025-05-30 23:49:19.3421 [f-304] router create: 127.0.0.1:30002 3631344834 0 127.0.0.1:59418
2025-05-30 23:49:19.3895 [f-304] kcp router syn: 3631344834 0 127.0.0.1:30002 127.0.0.1:59418
2025-05-30 23:49:19.3905 [f-1] channel create: 2147483666 3631344834 127.0.0.1:65305 Accept
2025-05-30 23:49:19.3906 [f-1] session create: zone: 1 id: 2147483666 1748620159390 
2025-05-30 23:49:19.3906 [f-1] kservice syn: 2147483666 3631344834 2147483666 127.0.0.1:65305
2025-05-30 23:49:19.3919 [f-304] kcp router ack: 3631344834 2147483666 127.0.0.1:59418
2025-05-30 23:49:19.6621 [f-1] tap tap login result: 0 cQPq2VZTPtrQUKHhblcZeg== Wd8SUnqB8IRT9hmXibjLOQ==
2025-05-30 23:49:20.3295 [f-302] router new: outerConn: 4162763144 innerConn: 0 127.0.0.1:59420
2025-05-30 23:49:20.3295 [f-302] router create: 127.0.0.1:30004 4162763144 0 127.0.0.1:59420
2025-05-30 23:49:20.3514 [f-302] kcp router syn: 4162763144 0 127.0.0.1:30004 127.0.0.1:59420
2025-05-30 23:49:20.3514 [f-304] kcp router outer fin: 3631344834 2147483666 127.0.0.1:30002
2025-05-30 23:49:20.3514 [f-304] kcp router outer fin: 3631344834 2147483666 127.0.0.1:30002
2025-05-30 23:49:20.3514 [f-304] kcp router outer fin: 3631344834 2147483666 127.0.0.1:30002
2025-05-30 23:49:20.3519 [f-1] kservice recv fin: 2147483666 3631344834 0
2025-05-30 23:49:20.3519 [f-1] channel dispose: 2147483666 3631344834 100208
2025-05-30 23:49:20.3519 [f-1] session dispose: 127.0.0.1:59418 id: 2147483666 ErrorCode: 100208, please see ErrorCode.cs! 1748620160352
2025-05-30 23:49:20.3519 [f-3] channel create: 2147483668 4162763144 127.0.0.1:59856 Accept
2025-05-30 23:49:20.3519 [f-3] session create: zone: 2 id: 2147483668 1748620160352 
2025-05-30 23:49:20.3519 [f-3] kservice syn: 2147483668 4162763144 2147483668 127.0.0.1:59856
2025-05-30 23:49:20.3537 [f-302] kcp router ack: 4162763144 2147483668 127.0.0.1:59420
2025-05-30 23:49:36.7029 [f--5][r-745533198] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:49:40.1957 [f-9] ServerShowTiaoSaoShopMsg: 0
2025-05-30 23:49:50.3424 [f-304] TChannel OnError: 100208 127.0.0.1:59418
2025-05-30 23:49:50.3424 [f-304] channel dispose: 2147483665 127.0.0.1:59418 0
2025-05-30 23:50:00.6061 [f-304] router node remove: 3631344834 2147483666 110401
2025-05-30 23:50:00.6061 [f-304] router remove: 3631344834 outerConn: 3631344834 innerConn: 2147483666
2025-05-30 23:50:05.3237 [f-9] ServerShowTiaoSaoShopMsg: 0
2025-05-30 23:50:36.7044 [f--5][r-741600995] InnerGlobalTimerPer_1_MinMsgHandler
2025-05-30 23:50:37.6302 [f-302] TChannel OnError: 100208 127.0.0.1:59420
2025-05-30 23:50:37.6302 [f-302] channel dispose: 2147483667 127.0.0.1:59420 0
