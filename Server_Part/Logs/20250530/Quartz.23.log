2025-05-30 23:42:36.4363 INFO [Quartz.Impl.StdSchedulerFactory] Default Quartz.NET properties loaded from embedded resource file 
2025-05-30 23:42:36.6229 DEBUG [Quartz.Simpl.TaskSchedulingThreadPool] TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. 
2025-05-30 23:42:36.6247 INFO [Quartz.Core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-05-30 23:42:36.6247 INFO [Quartz.Core.QuartzScheduler] Quartz Scheduler created 
2025-05-30 23:42:36.6247 INFO [Quartz.Simpl.RAMJobStore] RAMJobStore initialized. 
2025-05-30 23:42:36.6247 INFO [Quartz.Impl.StdSchedulerFactory] Quartz Scheduler 3.14.0.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized 
2025-05-30 23:42:36.6247 INFO [Quartz.Impl.StdSchedulerFactory] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 
2025-05-30 23:42:36.6254 INFO [Quartz.Impl.StdSchedulerFactory] Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False 
2025-05-30 23:42:36.6275 INFO [Quartz.Core.QuartzScheduler] Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. 
2025-05-30 23:42:36.6275 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:42:36.7075 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:42:36.7075 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:42:36.7075 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:42:36.7075 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:42:36.7075 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:42:36.7088 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:43:02.9518 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:43:32.8602 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:43:36.7073 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:43:36.7099 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:43:36.7160 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:43:36.7197 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:44:02.0918 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:44:31.9416 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:44:36.7044 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:44:36.7056 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:44:36.7061 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:44:36.7061 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:45:03.8443 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:45:29.3776 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:45:36.7036 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:45:36.7047 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:45:36.7047 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:45:36.7060 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:46:04.4452 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:46:33.9748 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:46:36.7038 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:46:36.7082 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:46:36.7082 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:46:36.7094 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:47:03.5767 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:47:27.3452 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:47:36.7028 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:47:36.7034 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:47:36.7043 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:47:36.7051 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:47:36.7078 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_5_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:47:36.7078 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:47:36.7080 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_5_Min 
2025-05-30 23:47:36.7080 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:48:04.7708 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:48:28.5604 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:48:36.7033 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:48:36.7064 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:48:36.7078 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:48:36.7087 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:49:02.4489 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:49:27.5289 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:49:36.7022 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:49:36.7022 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:49:36.7022 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:49:36.7029 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 23:49:59.8779 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:50:28.5126 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 23:50:36.7030 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 23:50:36.7033 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 23:50:36.7033 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 23:50:36.7033 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
